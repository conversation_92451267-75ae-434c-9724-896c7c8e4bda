@import url("https://fonts.googleapis.com/css?family=Press+Start+2P");
body {
  margin: 0;
  display: flex;
  font-family: "Press Start 2P", cursive;
}

#controls {
  position: absolute;
  bottom: 20px;
  min-width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

#controls div {
  display: grid;
  grid-template-columns: 50px 50px 50px;
  gap: 10px;
}

#controls button {
  width: 100%;
  height: 40px;
  background-color: white;
  border: 1px solid lightgray;
  box-shadow: 3px 5px 0px 0px rgba(0, 0, 0, 0.75);
  cursor: pointer;
  outline: none;
}

#controls button:first-of-type {
  grid-column: 1/-1;
}

#score {
  position: absolute;
  top: 20px;
  left: 20px;

  font-size: 2em;
  color: white;
}

#result-container {
  position: absolute;
  min-width: 100%;
  min-height: 100%;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: hidden;

  #result {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: white;
    padding: 20px;
  }

  button {
    background-color: red;
    padding: 20px 50px 20px 50px;
    font-family: inherit;
    font-size: inherit;
    cursor: pointer;
  }
}


#youtube,
#youtube-card {
  display: none;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  color: black;
}

@media (min-height: 425px) {
  /** Youtube logo by https://codepen.io/alvaromontoro */
  #youtube {
    z-index: 50;
    width: 100px;
    display: block;
    height: 70px;
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: red;
    border-radius: 50% / 11%;
    transform: scale(0.8);
    transition: transform 0.5s;
  }

  #youtube:hover,
  #youtube:focus {
    transform: scale(0.9);
    color: black;
  }

  #youtube::before {
    content: "";
    display: block;
    position: absolute;
    top: 7.5%;
    left: -6%;
    width: 112%;
    height: 85%;
    background: red;
    border-radius: 9% / 50%;
  }

  #youtube::after {
    content: "";
    display: block;
    position: absolute;
    top: 20px;
    left: 40px;
    width: 45px;
    height: 30px;
    border: 15px solid transparent;
    box-sizing: border-box;
    border-left: 30px solid white;
  }

  #youtube span {
    font-size: 0;
    position: absolute;
    width: 0;
    height: 0;
    overflow: hidden;
  }

  #youtube:hover + #youtube-card {
    z-index: 49;
    display: block;
    position: fixed;
    bottom: 12px;
    right: 10px;
    padding: 25px 130px 25px 25px;
    width: 300px;
    background-color: white;
  }
}
